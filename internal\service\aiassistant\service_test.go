package aiassistant

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// Mock services
type MockDashboardService struct {
	mock.Mock
}

func (m *MockDashboardService) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialMap), args.Error(1)
}

func (m *MockDashboardService) FindFinancialStress(ctx context.Context, userID string, period string) (*dashboard.FinancialStress, error) {
	args := m.Called(ctx, userID, period)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialStress), args.Error(1)
}

func (m *MockDashboardService) FindNetWorthHistory(ctx context.Context, userID string, months int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, months)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

// Stub implementations for other methods not used in tests
func (m *MockDashboardService) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount) (*dashboard.IncomeSource, error) {
	return nil, nil
}

func (m *MockDashboardService) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	return nil, nil
}

func (m *MockDashboardService) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	return nil
}

func (m *MockDashboardService) DeleteIncomeSource(ctx context.Context, id string) error {
	return nil
}

func (m *MockDashboardService) UpdateStrategicFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	return nil
}

func (m *MockDashboardService) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	return nil, nil
}

func (m *MockDashboardService) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	return nil, nil
}

func (m *MockDashboardService) UpdateInvestment(ctx context.Context, userID string, id string, name string, currentValue monetary.Amount) error {
	return nil
}

func (m *MockDashboardService) DeleteInvestment(ctx context.Context, userID string, id string) error {
	return nil
}

func (m *MockDashboardService) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	return nil, nil
}

func (m *MockDashboardService) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	return nil, nil
}

func (m *MockDashboardService) UpdateAsset(ctx context.Context, userID string, id string, description string, value monetary.Amount) error {
	return nil
}

func (m *MockDashboardService) DeleteAsset(ctx context.Context, userID string, id string) error {
	return nil
}

func (m *MockDashboardService) FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	return nil, nil
}

func (m *MockDashboardService) UpdateRetirementTargetAmount(ctx context.Context, userID string, targetAmount monetary.Amount) error {
	return nil
}

// Missing methods from dashboard service interface
func (m *MockDashboardService) FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	return nil, nil
}

func (m *MockDashboardService) UpdateStrategicFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	return nil
}

func (m *MockDashboardService) SyncNetworthForExistingUser(ctx context.Context, userID string) error {
	return nil
}

func (m *MockDashboardService) FindFixedExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	return nil, nil
}

func (m *MockDashboardService) FindVariableExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	return nil, nil
}

func (m *MockDashboardService) FindDebtExpenses(ctx context.Context, userID string, period string) (*dashboard.ExpenseCategory, error) {
	return nil, nil
}

func (m *MockDashboardService) FindExpenseAnalysisDetails(ctx context.Context, userID string, categoryID string, period string) (*dashboard.CategoryBreakdown, error) {
	return nil, nil
}

func (m *MockDashboardService) GetPortugueseMonthName(month time.Month) string {
	return ""
}

// Test suite
type AIAssistantServiceTestSuite struct {
	suite.Suite
	service              Service
	mockDashboardService *MockDashboardService
	testUserID           string
}

func (suite *AIAssistantServiceTestSuite) SetupTest() {
	suite.mockDashboardService = new(MockDashboardService)
	suite.service = New(
		nil, // userService - not used in FinancialHealthOverview
		suite.mockDashboardService,
		nil, // dreamboardService - not used in FinancialHealthOverview
		nil, // financialDNAService - not used in FinancialHealthOverview
		nil, // financialSheetService - not used in FinancialHealthOverview
		nil, // progressionService - not used in FinancialHealthOverview
		nil, // vaultService - not used in FinancialHealthOverview
		nil, // walletService - not used in FinancialHealthOverview
		nil, // achievementService - not used in FinancialHealthOverview
		nil, // trailService - not used in FinancialHealthOverview
		nil, // gamificationService - not used in FinancialHealthOverview
	)
	suite.testUserID = "test-user-123"
}

func TestAIAssistantServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AIAssistantServiceTestSuite))
}

func (suite *AIAssistantServiceTestSuite) TestFinancialHealthOverview_Success() {
	ctx := context.Background()

	// Mock financial map
	financialMap := &dashboard.FinancialMap{
		UserID:           suite.testUserID,
		MonthlyIncome:    10000,
		TotalInvestments: 50000,
		TotalAssets:      100000,
		StrategicFund: &dashboard.StrategicFund{
			CurrentValue: 20000,
			GoalValue:    60000,
		},
	}

	// Mock financial stress
	financialStress := &dashboard.FinancialStress{
		CommitmentAnalysis: &dashboard.CommitmentAnalysis{
			FixedExpense: dashboard.CommitmentDetail{
				Percentage: 30.0,
			},
			VariableExpense: dashboard.CommitmentDetail{
				Percentage: 20.0,
			},
			Debt: dashboard.CommitmentDetail{
				Percentage: 10.0,
			},
		},
		SpendingVariation: &dashboard.SpendingVariation{
			TotalChange: dashboard.VariationDetail{
				Percentage: 5.0,
			},
		},
	}

	// Mock net worth history
	netWorthHistory := []*dashboard.NetWorthSnapshot{
		{
			Date:       time.Now(),
			TotalValue: 170000, // Current month
		},
		{
			Date:       time.Now().AddDate(0, -1, 0),
			TotalValue: 160000, // Previous month
		},
	}

	// Setup mocks
	suite.mockDashboardService.On("FindFinancialMap", ctx, suite.testUserID).Return(financialMap, nil)
	suite.mockDashboardService.On("FindFinancialStress", ctx, suite.testUserID, "30d").Return(financialStress, nil)
	suite.mockDashboardService.On("FindNetWorthHistory", ctx, suite.testUserID, 6).Return(netWorthHistory, nil)

	// Call the method
	result, err := suite.service.FinancialHealthOverview(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(result)
	suite.Equal(suite.testUserID, result.UserID)

	// Verify monthly financials
	suite.Equal(int64(10000), result.MonthlyFinancials.Income)
	// Note: MonthlyFinancialsDTO doesn't have StrategicFund, Investments, Assets fields
	// These are in NetWorthData instead

	// Verify stress metrics
	suite.Equal(0.6, result.StressMetrics.CommitmentRatio)   // (30+20+10)/100
	suite.Equal(0.1, result.StressMetrics.DebtToIncomeRatio) // 10/100
	suite.Equal(5.0, result.StressMetrics.ExpenseGrowthRate)

	// Verify net worth data
	suite.Equal(int64(170000), result.NetWorthData.Current)
	suite.Equal(int64(160000), result.NetWorthData.PreviousMonth)
	suite.InDelta(6.25, result.NetWorthData.ChangePercent, 0.01) // (170000-160000)/160000*100

	// Verify historical trends
	suite.Len(result.HistoricalTrends, 2)
	suite.Equal(int64(170000), result.HistoricalTrends[0].NetWorth)
	suite.Equal(int64(160000), result.HistoricalTrends[1].NetWorth)

	suite.mockDashboardService.AssertExpectations(suite.T())
}

func (suite *AIAssistantServiceTestSuite) TestFinancialHealthOverview_PartialData() {
	ctx := context.Background()

	// Mock financial map with minimal data
	financialMap := &dashboard.FinancialMap{
		UserID:           suite.testUserID,
		MonthlyIncome:    5000,
		TotalInvestments: 0,
		TotalAssets:      0,
		StrategicFund:    nil, // No strategic fund
	}

	// Setup mocks - financial stress and net worth history return nil (no data)
	suite.mockDashboardService.On("FindFinancialMap", ctx, suite.testUserID).Return(financialMap, nil)
	suite.mockDashboardService.On("FindFinancialStress", ctx, suite.testUserID, "30d").Return(nil, nil)
	suite.mockDashboardService.On("FindNetWorthHistory", ctx, suite.testUserID, 6).Return([]*dashboard.NetWorthSnapshot{}, nil)

	// Call the method
	result, err := suite.service.FinancialHealthOverview(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(result)
	suite.Equal(suite.testUserID, result.UserID)

	// Verify monthly financials with partial data
	suite.Equal(int64(5000), result.MonthlyFinancials.Income)
	// Note: MonthlyFinancialsDTO doesn't have StrategicFund, Investments, Assets fields

	// Verify stress metrics with no data (should be zero values)
	suite.Equal(0.0, result.StressMetrics.CommitmentRatio)
	suite.Equal(0.0, result.StressMetrics.DebtToIncomeRatio)
	suite.Equal(0.0, result.StressMetrics.ExpenseGrowthRate)

	// Verify net worth data with minimal data
	suite.Equal(int64(0), result.NetWorthData.Current) // strategic fund (0) + investments (0) + assets (0)
	suite.Equal(int64(0), result.NetWorthData.PreviousMonth)
	suite.Equal(0.0, result.NetWorthData.ChangePercent)

	// Verify empty historical trends
	suite.Len(result.HistoricalTrends, 0)

	suite.mockDashboardService.AssertExpectations(suite.T())
}

func (suite *AIAssistantServiceTestSuite) TestBuildStressMetrics() {
	// Test with complete financial stress data
	financialStress := &dashboard.FinancialStress{
		CommitmentAnalysis: &dashboard.CommitmentAnalysis{
			FixedExpense: dashboard.CommitmentDetail{
				Percentage: 40.0,
			},
			VariableExpense: dashboard.CommitmentDetail{
				Percentage: 25.0,
			},
			Debt: dashboard.CommitmentDetail{
				Percentage: 15.0,
			},
		},
		SpendingVariation: &dashboard.SpendingVariation{
			TotalChange: dashboard.VariationDetail{
				Percentage: -2.5,
			},
		},
	}

	result := suite.service.(*service).buildStressMetrics(financialStress)

	suite.Equal(0.8, result.CommitmentRatio)    // (40+25+15)/100
	suite.Equal(0.15, result.DebtToIncomeRatio) // 15/100
	suite.Equal(-2.5, result.ExpenseGrowthRate)
	suite.Equal(0.0, result.EmergencyFundMonths) // TODO: implement calculation

	// Test with nil financial stress
	nilResult := suite.service.(*service).buildStressMetrics(nil)
	suite.Equal(0.0, nilResult.CommitmentRatio)
	suite.Equal(0.0, nilResult.DebtToIncomeRatio)
	suite.Equal(0.0, nilResult.ExpenseGrowthRate)
	suite.Equal(0.0, nilResult.EmergencyFundMonths)
}

func (suite *AIAssistantServiceTestSuite) TestBuildNetWorthData() {
	// Test with complete data
	financialMap := &dashboard.FinancialMap{
		TotalInvestments: 30000,
		TotalAssets:      70000,
		StrategicFund: &dashboard.StrategicFund{
			CurrentValue: 15000,
		},
	}

	netWorthHistory := []*dashboard.NetWorthSnapshot{
		{
			Date:       time.Now(),
			TotalValue: 115000, // Current
		},
		{
			Date:       time.Now().AddDate(0, -1, 0),
			TotalValue: 110000, // Previous month
		},
	}

	result := suite.service.(*service).buildNetWorthData(financialMap, netWorthHistory)

	suite.Equal(int64(115000), result.Current) // 15000 + 30000 + 70000
	suite.Equal(int64(110000), result.PreviousMonth)
	suite.InDelta(4.55, result.ChangePercent, 0.01) // (115000-110000)/110000*100
	suite.Equal(int64(15000), result.StrategicFund)
	suite.Equal(int64(30000), result.Investments)
	suite.Equal(int64(70000), result.Assets)
	suite.Equal(int64(0), result.Debts) // TODO: implement debt tracking

	// Test with no strategic fund
	financialMapNoFund := &dashboard.FinancialMap{
		TotalInvestments: 25000,
		TotalAssets:      50000,
		StrategicFund:    nil,
	}

	resultNoFund := suite.service.(*service).buildNetWorthData(financialMapNoFund, []*dashboard.NetWorthSnapshot{})

	suite.Equal(int64(75000), resultNoFund.Current) // 0 + 25000 + 50000
	suite.Equal(int64(0), resultNoFund.PreviousMonth)
	suite.Equal(0.0, resultNoFund.ChangePercent)
	suite.Equal(int64(0), resultNoFund.StrategicFund)
}

func (suite *AIAssistantServiceTestSuite) TestBuildHistoricalTrends() {
	netWorthHistory := []*dashboard.NetWorthSnapshot{
		{
			Date:       time.Date(2024, 12, 1, 0, 0, 0, 0, time.UTC),
			TotalValue: 120000,
		},
		{
			Date:       time.Date(2024, 11, 1, 0, 0, 0, 0, time.UTC),
			TotalValue: 115000,
		},
		{
			Date:       time.Date(2024, 10, 1, 0, 0, 0, 0, time.UTC),
			TotalValue: 110000,
		},
	}

	result := suite.service.(*service).buildHistoricalTrends(netWorthHistory)

	suite.Len(result, 3)
	suite.Equal("2024-12", result[0].Month)
	suite.Equal(int64(120000), result[0].NetWorth)
	suite.Equal(int64(0), result[0].Income)   // TODO: implement income tracking
	suite.Equal(int64(0), result[0].Expenses) // TODO: implement expense tracking

	suite.Equal("2024-11", result[1].Month)
	suite.Equal(int64(115000), result[1].NetWorth)

	suite.Equal("2024-10", result[2].Month)
	suite.Equal(int64(110000), result[2].NetWorth)

	// Test with empty history
	emptyResult := suite.service.(*service).buildHistoricalTrends([]*dashboard.NetWorthSnapshot{})
	suite.Len(emptyResult, 0)
}

func (suite *AIAssistantServiceTestSuite) TestBuildMonthlyFinancials() {
	// Test with complete financial map and stress data
	financialMap := &dashboard.FinancialMap{
		MonthlyIncome: 8000,
	}

	financialStress := &dashboard.FinancialStress{
		ExpenseAnalysis: &dashboard.ExpenseAnalysis{
			FixedExpenses: dashboard.ExpenseCategory{
				Total: 3000,
			},
			VariableExpenses: dashboard.ExpenseCategory{
				Total: 2000,
			},
			Debts: dashboard.ExpenseCategory{
				Total: 500,
			},
		},
	}

	result := suite.service.(*service).buildMonthlyFinancials(financialMap, financialStress)

	suite.Equal(int64(8000), result.Income)
	suite.Equal(int64(5500), result.Expenses)      // 3000 + 2000 + 500
	suite.Equal(int64(2500), result.Savings)       // 8000 - 5500
	suite.InDelta(31.25, result.SavingsRate, 0.01) // 2500/8000*100
	suite.Equal(int64(3000), result.FixedExpenses)
	suite.Equal(int64(2000), result.VariableExpenses)
	suite.Equal(int64(500), result.DebtPayments)

	// Test with nil financial stress
	resultNoStress := suite.service.(*service).buildMonthlyFinancials(financialMap, nil)

	suite.Equal(int64(8000), resultNoStress.Income)
	suite.Equal(int64(0), resultNoStress.Expenses)
	suite.Equal(int64(8000), resultNoStress.Savings) // All income becomes savings
	suite.Equal(100.0, resultNoStress.SavingsRate)   // 100% savings rate
	suite.Equal(int64(0), resultNoStress.FixedExpenses)
	suite.Equal(int64(0), resultNoStress.VariableExpenses)
	suite.Equal(int64(0), resultNoStress.DebtPayments)
}

// Mock services for LearningPathData tests
type MockProgressionService struct {
	mock.Mock
}

func (m *MockProgressionService) GetUserProgress(ctx context.Context, userID string) (*progression.ProgressSummary, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.ProgressSummary), args.Error(1)
}

func (m *MockProgressionService) GetUserEvents(ctx context.Context, userID string, limit int) ([]*progression.ProgressEvent, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.ProgressEvent), args.Error(1)
}

// Stub implementations for other methods not used in tests
func (m *MockProgressionService) CreateEvent(ctx context.Context, event *progression.ProgressEvent) error {
	return nil
}

func (m *MockProgressionService) UpdateProgress(ctx context.Context, userID string, trailID string, lessonID string, completed bool) error {
	return nil
}

func (m *MockProgressionService) CompleteTrail(ctx context.Context, userID string, trailID string) error {
	return nil
}

func (m *MockProgressionService) CompleteChallenge(ctx context.Context, userID string, trailID string, challengeID string) error {
	return nil
}

func (m *MockProgressionService) CheckUserPermission(ctx context.Context, userClassification string, trailId string) error {
	return nil
}

func (m *MockProgressionService) Initialize(ctx context.Context, userId string) error {
	return nil
}

func (m *MockProgressionService) GetTrailProgress(ctx context.Context, userID, userClassification string, trailID string) (*progression.TrailSummary, error) {
	return nil, nil
}

func (m *MockProgressionService) RecordProgress(ctx context.Context, userId string, body *progression.ProgressionBody) error {
	return nil
}

func (m *MockProgressionService) CreateLesson(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	return nil
}

func (m *MockProgressionService) CreateChallenge(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	return nil
}

func (m *MockProgressionService) LegacyCreateLessonChallenge(ctx context.Context, userId string, userClassification string, progressionBody *progression.ProgressionBody) error {
	return nil
}

func (m *MockProgressionService) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	return nil, nil
}

func (m *MockProgressionService) LegacyFindTrail(ctx context.Context, userId string, userClassification string, trailId string) (*progression.Trail, error) {
	return nil, nil
}

func (m *MockProgressionService) LegacyFindLesson(ctx context.Context, userId string, userClassification string, trailId string, lessonId string) (*progression.Lesson, error) {
	return nil, nil
}

func (m *MockProgressionService) LegacyFindChallenge(ctx context.Context, userId string, userClassification string, trailId string, challengeId string) (*progression.Challenge, error) {
	return nil, nil
}

func (m *MockProgressionService) LegacyFindChallengePhase(ctx context.Context, userId string, userClassification string, trailId string, challengeId string, challengePhase string) (*progression.ChallengePhase, error) {
	return nil, nil
}

type MockGamificationService struct {
	mock.Mock
}

func (m *MockGamificationService) FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*gamification.Achievement), args.Error(1)
}

func (m *MockGamificationService) FindContentAchievements(ctx context.Context) ([]*content.Achievement, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.Achievement), args.Error(1)
}

// Stub implementations for other methods not used in tests
func (m *MockGamificationService) CreateAchievement(ctx context.Context, userID string, identifier string) error {
	return nil
}

type MockTrailService struct {
	mock.Mock
}

func (m *MockTrailService) Find(ctx context.Context, id string) (*content.Trail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

// Stub implementations for other methods not used in tests
func (m *MockTrailService) FindAll(ctx context.Context) ([]*content.Trail, error) {
	return nil, nil
}

func (m *MockTrailService) Create(ctx context.Context, trail *content.Trail) error {
	return nil
}

func (m *MockTrailService) Update(ctx context.Context, id string, trail *content.Trail) error {
	return nil
}

func (m *MockTrailService) Delete(ctx context.Context, id string) error {
	return nil
}

// Test for time calculation optimization
func (suite *AIAssistantServiceTestSuite) TestTimeCalculationOptimization() {
	// Test the parseTimeStringToMinutes function
	service := suite.service.(*service)

	// Test various time string formats
	testCases := []struct {
		input    string
		expected int
	}{
		{"N/A", 0},
		{"< 1m", 0},
		{"31m", 31},
		{"1h", 60},
		{"2h 30m", 150},
		{"655h 0m", 39300}, // This should be parsed correctly but is unrealistic
	}

	for _, tc := range testCases {
		result := service.parseTimeStringToMinutes(tc.input)
		suite.Equal(tc.expected, result, "Failed to parse time string: %s", tc.input)
	}
}

// Test for formatDuration function
func (suite *AIAssistantServiceTestSuite) TestFormatDuration() {
	service := suite.service.(*service)

	testCases := []struct {
		input    int
		expected string
	}{
		{0, "< 1m"},
		{30, "30m"},
		{60, "1h"},
		{90, "1h 30m"},
		{120, "2h"},
		{150, "2h 30m"},
	}

	for _, tc := range testCases {
		result := service.formatDuration(tc.input)
		suite.Equal(tc.expected, result, "Failed to format duration: %d minutes", tc.input)
	}
}

// Test for calculateEngagementFactor function
func (suite *AIAssistantServiceTestSuite) TestCalculateEngagementFactor() {
	service := suite.service.(*service)

	testCases := []struct {
		eventCount   int
		contentItems int
		expected     int
	}{
		{6, 2, 2}, // 3.0 events per item = high engagement
		{3, 2, 1}, // 1.5 events per item = normal engagement
		{1, 2, 1}, // 0.5 events per item = low engagement (but still minimum)
		{0, 2, 1}, // 0 events per item = minimum engagement
		{5, 0, 1}, // Division by zero protection
	}

	for _, tc := range testCases {
		result := service.calculateEngagementFactor(tc.eventCount, tc.contentItems)
		suite.Equal(tc.expected, result, "Failed engagement factor calculation for %d events, %d items", tc.eventCount, tc.contentItems)
	}
}

// Test for calculateRealisticTimeFromEvents function
func (suite *AIAssistantServiceTestSuite) TestCalculateRealisticTimeFromEvents() {
	service := suite.service.(*service)

	// Test with no events
	result := service.calculateRealisticTimeFromEvents([]time.Time{})
	suite.Equal("N/A", result)

	// Test with few events (should get minimum time)
	events := []time.Time{
		time.Now(),
		time.Now().Add(time.Minute),
	}
	result = service.calculateRealisticTimeFromEvents(events)
	suite.Equal("5m", result) // Minimum 5 minutes

	// Test with many events (should be capped)
	manyEvents := make([]time.Time, 100)
	for i := range manyEvents {
		manyEvents[i] = time.Now().Add(time.Duration(i) * time.Minute)
	}
	result = service.calculateRealisticTimeFromEvents(manyEvents)
	suite.Equal("2h", result) // Capped at 120 minutes
}

// Helper function to parse time strings to minutes for testing
func (suite *AIAssistantServiceTestSuite) parseTimeToMinutes(timeStr string) int {
	if timeStr == "N/A" || timeStr == "< 1m" {
		return 0
	}

	// Simple parsing for test purposes
	if timeStr == "31m" {
		return 31
	}
	if timeStr == "1h" {
		return 60
	}
	if timeStr == "2h 30m" {
		return 150
	}

	// For other formats, assume reasonable values for testing
	return 30 // Default reasonable value
}
